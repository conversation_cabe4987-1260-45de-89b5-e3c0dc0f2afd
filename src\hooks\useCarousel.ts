import { useState, useCallback, useRef } from 'react';

export interface UseCarouselProps {
  totalItems: number;
  itemsPerSlide: number;
  autoLoop?: boolean;
}

export interface UseCarouselReturn {
  currentSlide: number;
  maxSlides: number;
  nextSlide: () => void;
  prevSlide: () => void;
  goToSlide: (slide: number) => void;
  touchHandlers: {
    onTouchStart: (e: React.TouchEvent) => void;
    onTouchMove: (e: React.TouchEvent) => void;
    onTouchEnd: () => void;
  };
}

export function useCarousel({ 
  totalItems, 
  itemsPerSlide, 
  autoLoop = true 
}: UseCarouselProps): UseCarouselReturn {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  
  const maxSlides = Math.ceil(totalItems / itemsPerSlide);

  const nextSlide = useCallback(() => {
    setCurrentSlide(prev => {
      if (autoLoop) {
        return (prev + 1) % maxSlides;
      }
      return Math.min(prev + 1, maxSlides - 1);
    });
  }, [maxSlides, autoLoop]);

  const prevSlide = useCallback(() => {
    setCurrentSlide(prev => {
      if (autoLoop) {
        return (prev - 1 + maxSlides) % maxSlides;
      }
      return Math.max(prev - 1, 0);
    });
  }, [maxSlides, autoLoop]);

  const goToSlide = useCallback((slide: number) => {
    if (slide >= 0 && slide < maxSlides) {
      setCurrentSlide(slide);
    }
  }, [maxSlides]);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
  }, []);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  }, []);

  const handleTouchEnd = useCallback(() => {
    const swipeThreshold = 75;
    
    // Swipe left (next slide)
    if (touchStart - touchEnd > swipeThreshold) {
      nextSlide();
    }
    // Swipe right (previous slide)
    if (touchStart - touchEnd < -swipeThreshold) {
      prevSlide();
    }
  }, [touchStart, touchEnd, nextSlide, prevSlide]);

  return {
    currentSlide,
    maxSlides,
    nextSlide,
    prevSlide,
    goToSlide,
    touchHandlers: {
      onTouchStart: handleTouchStart,
      onTouchMove: handleTouchMove,
      onTouchEnd: handleTouchEnd
    }
  };
}
