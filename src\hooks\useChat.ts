import { useState, useCallback } from 'react';
import { Message } from '@/types/chat';
import { generateId } from '@/lib/utils';

export interface UseChatReturn {
  messages: Message[];
  currentMessage: string;
  isLoading: boolean;
  error: string | null;
  sendMessage: (message: string) => void;
  setCurrentMessage: (message: string) => void;
  clearMessages: () => void;
  handleSuggestionClick: (title: string) => void;
}

export function useChat(): UseChatReturn {
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const sendMessage = useCallback((message: string) => {
    if (!message.trim()) return;

    const newMessage: Message = {
      id: generateId(),
      text: message.trim(),
      timestamp: Date.now(),
      type: 'user'
    };

    setMessages(prev => [...prev, newMessage]);
    setCurrentMessage('');
    setError(null);
  }, []);

  const handleSuggestionClick = useCallback((title: string) => {
    sendMessage(title);
  }, [sendMessage]);

  const clearMessages = useCallback(() => {
    setMessages([]);
    setCurrentMessage('');
    setError(null);
  }, []);

  return {
    messages,
    currentMessage,
    isLoading,
    error,
    sendMessage,
    setCurrentMessage,
    clearMessages,
    handleSuggestionClick
  };
}
