import React, { forwardRef } from 'react';
import { InputProps } from '@/types/ui';
import { cn } from '@/lib/utils';

export const Input = forwardRef<HTMLInputElement, InputProps>(
  ({
    type = 'text',
    placeholder,
    value,
    onChange,
    onSubmit,
    disabled = false,
    autoFocus = false,
    className,
    children,
    ...props
  }, ref) => {
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter' && onSubmit) {
        e.preventDefault();
        onSubmit();
      }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (onChange) {
        onChange(e.target.value);
      }
    };

    return (
      <div className="relative">
        <input
          ref={ref}
          type={type}
          value={value}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          autoFocus={autoFocus}
          className={cn(
            'w-full pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md transition-all duration-200 focus:border-gray-400 focus:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed',
            className
          )}
          {...props}
        />
        {children}
      </div>
    );
  }
);

Input.displayName = 'Input';
