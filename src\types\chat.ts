export interface Message {
  id: string;
  text: string;
  timestamp: number;
  type?: 'user' | 'assistant';
}

export interface SuggestionCard {
  title: string;
  subtitle: string;
}

export interface ChatState {
  messages: Message[];
  currentMessage: string;
  isLoading: boolean;
  error: string | null;
}

export interface ChatActions {
  sendMessage: (message: string) => void;
  setCurrentMessage: (message: string) => void;
  clearMessages: () => void;
  handleSuggestionClick: (title: string) => void;
}

export type ChatContextType = ChatState & ChatActions;
