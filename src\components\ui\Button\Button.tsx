import React from 'react';
import { ButtonProps } from '@/types/ui';
import { cn } from '@/lib/utils';

const buttonVariants = {
  primary: 'bg-black text-white hover:bg-gray-800',
  secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300',
  outline: 'border border-gray-200 bg-white text-gray-900 hover:bg-gray-50',
  ghost: 'text-gray-900 hover:bg-gray-100'
};

const buttonSizes = {
  sm: 'h-8 w-8 text-sm',
  md: 'h-10 px-4 py-2',
  lg: 'h-12 px-6 py-3 text-lg'
};

export function Button({
  variant = 'primary',
  size = 'md',
  className,
  children,
  disabled = false,
  loading = false,
  onClick,
  type = 'button',
  ...props
}: ButtonProps) {
  return (
    <button
      type={type}
      className={cn(
        'inline-flex items-center justify-center rounded-full font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed',
        buttonVariants[variant],
        buttonSizes[size],
        className
      )}
      disabled={disabled || loading}
      onClick={onClick}
      {...props}
    >
      {loading ? (
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
      ) : (
        children
      )}
    </button>
  );
}
