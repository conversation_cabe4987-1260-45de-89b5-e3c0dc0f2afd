import React from 'react';
import { CardProps } from '@/types/ui';
import { cn } from '@/lib/utils';

export function Card({
  title,
  subtitle,
  onClick,
  hover = true,
  className,
  children,
  ...props
}: CardProps) {
  const Component = onClick ? 'button' : 'div';

  return (
    <Component
      onClick={onClick}
      className={cn(
        'rounded-[12px] bg-gray-200 text-left transition-all duration-200',
        hover && 'hover:bg-[#D6EDFF]',
        onClick && 'cursor-pointer focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
        className
      )}
      {...props}
    >
      {title || subtitle ? (
        <div className="p-4">
          {title && (
            <div className="text-[16px] font-[600] text-black mb-1">
              {title}
            </div>
          )}
          {subtitle && (
            <div className="text-[16px] text-gray-500">
              {subtitle}
            </div>
          )}
        </div>
      ) : (
        children
      )}
    </Component>
  );
}
