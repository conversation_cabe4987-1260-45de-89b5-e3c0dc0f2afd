import { ReactNode } from 'react';

export interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

export interface InputProps extends BaseComponentProps {
  type?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onSubmit?: () => void;
  disabled?: boolean;
  autoFocus?: boolean;
  ref?: React.RefObject<HTMLInputElement>;
}

export interface CardProps extends BaseComponentProps {
  title?: string;
  subtitle?: string;
  onClick?: () => void;
  hover?: boolean;
}

export interface CarouselProps extends BaseComponentProps {
  items: any[];
  currentSlide: number;
  onNext: () => void;
  onPrev: () => void;
  itemsPerSlide?: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
}

export interface ResponsiveBreakpoint {
  mobile: boolean;
  tablet: boolean;
  desktop: boolean;
}
